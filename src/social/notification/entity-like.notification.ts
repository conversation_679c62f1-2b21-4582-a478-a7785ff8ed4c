import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityValidatorService } from '../service/entity-validator.service';
import { UserProvider } from '../../user/service/provider';

/**
 * Base notification data for like-related notifications
 */
export interface BaseLikeNotificationData {
  likeId: string;
  entityType: string;
  entityId: string;
  userId: string;
  timestamp: Date;
}

/**
 * Notification data for entity liked notifications
 */
export interface EntityLikedNotificationData extends BaseLikeNotificationData {
  entityTitle?: string;
  entityThumbnail?: string;
  entityOwnerId: string;
}

/**
 * Notification data for like milestone notifications
 */
export interface LikeMilestoneNotificationData
  extends BaseLikeNotificationData {
  milestone: number;
  totalLikes: number;
  entityTitle?: string;
  entityThumbnail?: string;
  entityOwnerId: string;
}

/**
 * Service to handle like-related notifications
 */
@Injectable()
export class EntityLikeNotificationService {
  constructor(
    private logger: Logger,
    private entityValidatorService: EntityValidatorService,
    private userProvider: UserProvider,
  ) {}

  /**
   * Handle entity liked events
   */
  @OnEvent('entity.liked')
  async handleEntityLiked(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    try {
      // Get entity information
      const entityInfo = await this.entityValidatorService.getEntityInfo(
        payload.entityType,
        payload.entityId,
      );

      // Don't notify if user is liking their own entity
      if (entityInfo.ownerId === payload.userId) {
        return;
      }

      // Get liker information
      const liker = await this.userProvider.get(payload.userId);
      if (!liker) {
        this.logger.warn('Liker not found for notification', {
          userId: payload.userId,
          likeId: payload.likeId,
        });
        return;
      }

      // Create notification data
      const notificationData: EntityLikedNotificationData = {
        likeId: payload.likeId,
        entityType: payload.entityType,
        entityId: payload.entityId,
        userId: payload.userId,
        entityTitle: entityInfo.title,
        entityThumbnail: entityInfo.thumbnail,
        entityOwnerId: entityInfo.ownerId,
        timestamp: new Date(),
      };

      // Generate notification message
      const message = this.generateEntityLikedMessage(
        liker.username,
        payload.entityType,
        entityInfo.title,
      );

      // TODO: Integrate with actual notification system
      this.logger.log('Entity liked notification', {
        recipientId: entityInfo.ownerId,
        message,
        data: notificationData,
      });

      // Check for milestone and emit milestone event if applicable
      await this.checkAndEmitMilestone(
        payload.likeId,
        payload.entityType,
        payload.entityId,
        payload.userId,
        entityInfo,
      );
    } catch (error) {
      this.logger.error('Failed to handle entity liked notification', {
        error: error.message,
        payload,
      });
    }
  }

  /**
   * Handle like milestone events
   */
  @OnEvent('entity.like.milestone')
  async handleLikeMilestone(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
    milestone: number;
    totalLikes: number;
    entityOwnerId?: string;
  }): Promise<void> {
    try {
      // Get entity information
      const entityInfo = await this.entityValidatorService.getEntityInfo(
        payload.entityType,
        payload.entityId,
      );

      const entityOwnerId = payload.entityOwnerId || entityInfo.ownerId;

      // Create notification data
      const notificationData: LikeMilestoneNotificationData = {
        likeId: payload.likeId,
        entityType: payload.entityType,
        entityId: payload.entityId,
        userId: payload.userId,
        milestone: payload.milestone,
        totalLikes: payload.totalLikes,
        entityTitle: entityInfo.title,
        entityThumbnail: entityInfo.thumbnail,
        entityOwnerId,
        timestamp: new Date(),
      };

      // Generate notification message
      const message = this.generateMilestoneMessage(
        payload.milestone,
        payload.entityType,
        entityInfo.title,
      );

      // TODO: Integrate with actual notification system
      this.logger.log('Like milestone notification', {
        recipientId: entityOwnerId,
        message,
        data: notificationData,
      });
    } catch (error) {
      this.logger.error('Failed to handle like milestone notification', {
        error: error.message,
        payload,
      });
    }
  }

  /**
   * Check if the current like count represents a milestone and emit event
   */
  private async checkAndEmitMilestone(
    likeId: string,
    entityType: string,
    entityId: string,
    userId: string,
    entityInfo: any,
  ): Promise<void> {
    try {
      const currentLikes = entityInfo.likes || 0;

      // Check if this is a milestone
      if (this.isMilestone(currentLikes)) {
        // Emit milestone event
        // TODO: Use proper event emitter
        this.logger.log('Like milestone reached', {
          entityType,
          entityId,
          milestone: currentLikes,
          totalLikes: currentLikes,
        });
      }
    } catch (error) {
      this.logger.error('Failed to check milestone', {
        error: error.message,
        entityType,
        entityId,
      });
    }
  }

  /**
   * Generate message for entity liked notifications
   */
  private generateEntityLikedMessage(
    likerUsername: string,
    entityType: string,
    entityTitle?: string,
  ): string {
    const entityName = entityTitle || `your ${entityType}`;
    return `${likerUsername} liked ${entityName}`;
  }

  /**
   * Generate message for milestone notifications
   */
  private generateMilestoneMessage(
    milestone: number,
    entityType: string,
    entityTitle?: string,
  ): string {
    const entityName = entityTitle || `your ${entityType}`;
    return `🎉 ${entityName} reached ${milestone} likes!`;
  }

  /**
   * Check if a like count represents a milestone
   */
  private isMilestone(likeCount: number): boolean {
    // Define milestones: 1, 5, 10, 25, 50, 100, 250, 500, 1000, etc.
    const milestones = [
      1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000,
    ];
    return milestones.includes(likeCount);
  }

  /**
   * Get notification type for like events
   */
  getNotificationType(eventType: string): string {
    switch (eventType) {
      case 'entity.liked':
        return 'ENTITY_LIKED';
      case 'entity.unliked':
        return 'ENTITY_UNLIKED';
      case 'entity.like.milestone':
        return 'LIKE_MILESTONE';
      default:
        return 'LIKE_UNKNOWN';
    }
  }

  /**
   * Check if user should receive notifications for this entity type
   */
  private async shouldNotifyUser(
    userId: string,
    entityType: string,
    notificationType: string,
  ): Promise<boolean> {
    // TODO: Implement user notification preferences check
    // This would check user settings to see if they want notifications
    // for this type of event on this type of entity
    return true;
  }

  /**
   * Get entity-specific notification settings
   */
  private getEntitySpecificSettings(entityType: string): {
    enableLikeNotifications: boolean;
    enableMilestoneNotifications: boolean;
    milestoneThreshold: number;
  } {
    // TODO: Implement entity-specific notification settings
    // Different entity types might have different notification preferences
    return {
      enableLikeNotifications: true,
      enableMilestoneNotifications: true,
      milestoneThreshold: 1,
    };
  }
}
