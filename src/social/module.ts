import { <PERSON><PERSON><PERSON>, OnModuleInit, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserModule } from '../user/user.module';
import { AuthModule } from '../auth/auth.module';
import { VideoModule } from '../video/module';
import { ImageCompletionModule } from '../image-completion/module';
import { NotificationModule } from '../notification/module';

// Entities
import { EntityCommentEntity } from './entity/entity-comment.entity';
import { EntityLikeEntity } from './entity/entity-like.entity';
import { EntityCommentLikeEntity } from './entity/entity-comment-like.entity';
import { VideoEntity } from '../video/entity/video.entity';
import { ImageCompletionEntity } from '../image-completion/entity/image-completion.entity';

// Providers
import { EntityCommentProvider } from './service/entity-comment.provider';
import { EntityLikeProvider } from './service/entity-like.provider';
import { EntityCommentLikeProvider } from './service/entity-comment-like.provider';

// Managers
import { EntityCommentManager } from './service/entity-comment.manager';
import { EntityLikeManager } from './service/entity-like.manager';
import { EntityCommentLikeManager } from './service/entity-comment-like.manager';

// Request Managers
import { EntityCommentRequestManager } from './service/entity-comment.request-manager';
import { EntityLikeRequestManager } from './service/entity-like.request-manager';
import { EntityCommentLikeRequestManager } from './service/entity-comment-like.request-manager';
import { UserLikesRankingRequestManager } from './service/user-likes-ranking.request-manager';

// Validators
import { EntityValidatorService } from './service/entity-validator.service';
import { VideoEntityValidator } from './service/video-entity.validator';
import { ImageCompletionEntityValidator } from './service/image-completion-entity.validator';

// Controllers
import { EntityCommentController } from './controller/entity-comment.controller';
import { EntityLikeController } from './controller/entity-like.controller';
import { EntityCommentLikeController } from './controller/entity-comment-like.controller';
import { CommentResourceController } from './controller/comment-resource.controller';
import { UserActivityController } from './controller/user-activity.controller';
import { EntityStatsController } from './controller/entity-stats.controller';

// Response Mappers
import { EntityCommentResponseMapper } from './service/entity-comment.response-mapper';
import { EntityLikeResponseMapper } from './service/entity-like.response-mapper';
import { EntityCommentLikeResponseMapper } from './service/entity-comment-like.response-mapper';

// Notification Services
import { EntityCommentNotificationService } from './notification/entity-comment.notification';
import { EntityLikeNotificationService } from './notification/entity-like.notification';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Entity Interaction entities
      EntityCommentEntity,
      EntityLikeEntity,
      EntityCommentLikeEntity,
      // Entities for validators
      VideoEntity,
      ImageCompletionEntity,
    ]),
    EventEmitterModule,
    UserModule,
    AuthModule,
    NotificationModule,
    forwardRef(() => VideoModule), // Import VideoModule to access VideoProvider
    forwardRef(() => ImageCompletionModule), // Import ImageCompletionModule to access ImageCompletionProvider
  ],
  providers: [
    // Providers
    EntityCommentProvider,
    EntityLikeProvider,
    EntityCommentLikeProvider,

    // Managers
    EntityCommentManager,
    EntityLikeManager,
    EntityCommentLikeManager,

    // Request Managers
    EntityCommentRequestManager,
    EntityLikeRequestManager,
    EntityCommentLikeRequestManager,
    UserLikesRankingRequestManager,

    // Validators
    EntityValidatorService,
    VideoEntityValidator,
    ImageCompletionEntityValidator,

    // Response Mappers
    EntityCommentResponseMapper,
    EntityLikeResponseMapper,
    EntityCommentLikeResponseMapper,

    // Notification Services
    EntityCommentNotificationService,
    EntityLikeNotificationService,
  ],
  controllers: [
    // Register specific routes first (higher priority)
    UserActivityController, // /social/users/* - must come before parameterized routes

    // Register parameterized routes after specific routes
    EntityCommentController, // /social/:entityType/:entityId/comments
    EntityLikeController, // /social/:entityType/:entityId/likes
    EntityCommentLikeController, // /social/:entityType/:entityId/comments/:commentId/likes
    EntityStatsController, // /social/:entityType/:entityId/stats

    // Other controllers
    CommentResourceController,
  ],
  exports: [
    // Export providers for use in other modules
    EntityCommentProvider,
    EntityLikeProvider,
    EntityCommentLikeProvider,

    // Export managers for use in other modules
    EntityCommentManager,
    EntityLikeManager,
    EntityCommentLikeManager,

    // Export request managers for use in other modules
    EntityCommentRequestManager,
    EntityLikeRequestManager,
    EntityCommentLikeRequestManager,
    UserLikesRankingRequestManager,

    // Export validator service for use in other modules
    EntityValidatorService,

    // Export response mappers for use in other modules
    EntityCommentResponseMapper,
    EntityLikeResponseMapper,
    EntityCommentLikeResponseMapper,

    // Export notification services for use in other modules
    EntityCommentNotificationService,
    EntityLikeNotificationService,
  ],
})
export class SocialModule implements OnModuleInit {
  constructor(
    private entityValidatorService: EntityValidatorService,
    private videoEntityValidator: VideoEntityValidator,
    private imageCompletionEntityValidator: ImageCompletionEntityValidator,
  ) {}

  /**
   * Register entity validators when the module initializes
   */
  async onModuleInit() {
    // Register the video entity validator
    this.entityValidatorService.registerValidator(
      'video',
      this.videoEntityValidator,
    );

    // Register the image entity validator (using "image" as external API name)
    this.entityValidatorService.registerValidator(
      'image',
      this.imageCompletionEntityValidator,
    );

    // Log successful registration
    console.log(
      'SocialModule: Registered validators for entity types:',
      this.entityValidatorService.getRegisteredEntityTypes(),
    );
  }
}
