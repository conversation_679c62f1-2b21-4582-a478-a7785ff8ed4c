import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { VideoEntity, PrivacyEnum } from '../../video/entity/video.entity';
import { UserEntity } from '../../user/entity/user.entity';

export interface UnifiedEntity {
  entityType: string;
  entityId: string;
  title: string;
  description?: string;
  thumbnail?: string;
  owner: {
    id: string;
    username: string;
  };
  engagement: {
    likes: number;
    comments: number;
    engagementScore: number;
  };
  metadata: Record<string, any>;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  privacy: string;
  isUnsafe: boolean;
}

/**
 * Entity aggregator service providing unified interface across image and video entities
 */
@Injectable()
export class EntityAggregatorService {
  constructor(
    @InjectRepository(ImageCompletionEntity)
    private readonly imageRepository: Repository<ImageCompletionEntity>,

    @InjectRepository(VideoEntity)
    private readonly videoRepository: Repository<VideoEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,

    private readonly logger: Logger,
  ) {}

  /**
   * Get unified entity by type and ID
   */
  async getUnifiedEntity(
    entityType: string,
    entityId: string,
  ): Promise<UnifiedEntity | null> {
    try {
      this.logger.debug('Getting unified entity', { entityType, entityId });

      switch (entityType) {
        case 'image':
          return await this.getImageAsUnifiedEntity(entityId);
        case 'video':
          return await this.getVideoAsUnifiedEntity(entityId);
        default:
          this.logger.warn('Unknown entity type', { entityType });
          return null;
      }
    } catch (error) {
      this.logger.error('Failed to get unified entity', {
        entityType,
        entityId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Get multiple unified entities by type and IDs
   */
  async getUnifiedEntities(
    entities: Array<{ entityType: string; entityId: string }>,
  ): Promise<UnifiedEntity[]> {
    try {
      this.logger.log('Getting multiple unified entities', {
        count: entities.length,
        sampleEntities: entities.slice(0, 5), // Log first 5 entities
      });

      // Group entities by type
      const imageIds = entities
        .filter((e) => e.entityType === 'image')
        .map((e) => e.entityId);

      const videoIds = entities
        .filter((e) => e.entityType === 'video')
        .map((e) => e.entityId);

      // Fetch entities in parallel
      const [images, videos] = await Promise.all([
        imageIds.length > 0 ? this.getImagesAsUnifiedEntities(imageIds) : [],
        videoIds.length > 0 ? this.getVideosAsUnifiedEntities(videoIds) : [],
      ]);

      // Combine and maintain original order
      const entityMap = new Map<string, UnifiedEntity>();

      images.forEach((entity) => {
        entityMap.set(`image:${entity.entityId}`, entity);
      });

      videos.forEach((entity) => {
        entityMap.set(`video:${entity.entityId}`, entity);
      });

      // Return in original order
      const result = entities
        .map((e) => entityMap.get(`${e.entityType}:${e.entityId}`))
        .filter(Boolean) as UnifiedEntity[];

      this.logger.log('Unified entities aggregation complete', {
        requestedCount: entities.length,
        returnedCount: result.length,
        missingCount: entities.length - result.length,
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to get unified entities', {
        entityCount: entities.length,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Get entities by owner with unified interface
   */
  async getEntitiesByOwner(
    ownerId: string,
    entityType?: string,
    limit = 20,
    offset = 0,
  ): Promise<UnifiedEntity[]> {
    try {
      this.logger.debug('Getting entities by owner', {
        ownerId,
        entityType,
        limit,
        offset,
      });

      if (entityType === 'image') {
        const imageIds = await this.imageRepository
          .find({
            where: { userId: ownerId, deletedAt: null },
            select: ['id'],
            order: { createdAt: 'DESC' },
            take: limit,
            skip: offset,
          })
          .then((images) => images.map((img) => img.id));

        return await this.getImagesAsUnifiedEntities(imageIds);
      }

      if (entityType === 'video') {
        const videoIds = await this.videoRepository
          .find({
            where: { userId: ownerId, deletedAt: null },
            select: ['id'],
            order: { createdAt: 'DESC' },
            take: limit,
            skip: offset,
          })
          .then((videos) => videos.map((video) => video.id));

        return await this.getVideosAsUnifiedEntities(videoIds);
      }

      // Get both types
      const [images, videos] = await Promise.all([
        this.imageRepository.find({
          where: { userId: ownerId, deletedAt: null },
          select: ['id', 'createdAt'],
          order: { createdAt: 'DESC' },
          take: Math.ceil(limit / 2),
          skip: Math.floor(offset / 2),
        }),
        this.videoRepository.find({
          where: { userId: ownerId, deletedAt: null },
          select: ['id', 'createdAt'],
          order: { createdAt: 'DESC' },
          take: Math.ceil(limit / 2),
          skip: Math.floor(offset / 2),
        }),
      ]);

      // Combine and sort by creation date
      const allEntities = [
        ...images.map((img) => ({
          entityType: 'image',
          entityId: img.id,
          createdAt: img.createdAt,
        })),
        ...videos.map((video) => ({
          entityType: 'video',
          entityId: video.id,
          createdAt: video.createdAt,
        })),
      ]
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, limit);

      return await this.getUnifiedEntities(allEntities);
    } catch (error) {
      this.logger.error('Failed to get entities by owner', {
        ownerId,
        entityType,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Update entity engagement scores
   */
  async updateEntityEngagement(
    entityType: string,
    entityId: string,
    likes: number,
    comments: number,
  ): Promise<void> {
    try {
      this.logger.debug('Updating entity engagement', {
        entityType,
        entityId,
        likes,
        comments,
      });

      switch (entityType) {
        case 'image':
          await this.imageRepository.update(entityId, { likes, comments });
          break;
        case 'video':
          await this.videoRepository.update(entityId, { likes, comments });
          break;
        default:
          this.logger.warn('Unknown entity type for engagement update', {
            entityType,
          });
      }
    } catch (error) {
      this.logger.error('Failed to update entity engagement', {
        entityType,
        entityId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get image as unified entity
   */
  private async getImageAsUnifiedEntity(
    imageId: string,
  ): Promise<UnifiedEntity | null> {
    const image = await this.imageRepository.findOne({
      where: { id: imageId, deletedAt: null },
      relations: [
        'user',
        'originalImageCompletionRelations',
        'originalImageCompletionRelations.originalImageCompletion',
        'originalImageCompletionRelations.originalImageCompletion.user',
        'imageEditImageCompletion',
        'imageEditImageCompletion.imageEdit',
        'upscales',
        'models',
        'models.model',
      ],
    });

    if (!image) {
      return null;
    }

    return {
      entityType: 'image',
      entityId: image.id,
      title: image.prompt || 'Untitled Image',
      description: undefined,
      thumbnail: image.thumbnailUrl,
      owner: {
        id: image.user?.id || image.userId,
        username: image.user?.username || 'unknown',
      },
      engagement: {
        likes: image.likes || 0,
        comments: image.comments || 0,
        engagementScore: (image.likes || 0) + (image.comments || 0) * 2,
      },
      metadata: image.metadata || {},
      tags: image.searchTags || [],
      createdAt: image.createdAt,
      updatedAt: image.updatedAt,
      privacy: image.privacy || 'public',
      isUnsafe: image.isUnsafe || false,
    };
  }

  /**
   * Get video as unified entity
   */
  private async getVideoAsUnifiedEntity(
    videoId: string,
  ): Promise<UnifiedEntity | null> {
    const video = await this.videoRepository.findOne({
      where: {
        id: videoId,
        deletedAt: null,
        // REMOVED: Privacy filtering should be handled at the feed level, not here
        // The EntityAggregatorService should return entities regardless of privacy
        // Privacy enforcement happens in the feed service layer
      },
      relations: ['user'],
    });

    if (!video) {
      return null;
    }

    return {
      entityType: 'video',
      entityId: video.id,
      title: video.prompt || 'Untitled Video',
      description: video.description,
      thumbnail: video.thumbnailUrl,
      owner: {
        id: video.user?.id || video.userId,
        username: video.user?.username || 'unknown',
      },
      engagement: {
        likes: video.likes || 0,
        comments: video.comments || 0,
        engagementScore: (video.likes || 0) + (video.comments || 0) * 2,
      },
      metadata: video.metadata || {},
      tags: video.searchTags || [],
      createdAt: video.createdAt,
      updatedAt: video.updatedAt,
      privacy: video.privacy || 'public',
      isUnsafe: video.isUnsafe || false,
    };
  }

  /**
   * Get multiple images as unified entities
   */
  private async getImagesAsUnifiedEntities(
    imageIds: string[],
  ): Promise<UnifiedEntity[]> {
    if (imageIds.length === 0) return [];

    this.logger.debug('Fetching images from database', {
      imageIds: imageIds.slice(0, 5), // Log first 5 IDs
      totalCount: imageIds.length,
    });

    const images = await this.imageRepository.find({
      where: { id: In(imageIds), deletedAt: null },
      relations: ['user'],
    });

    this.logger.log('Images fetched from database', {
      requestedCount: imageIds.length,
      foundCount: images.length,
      foundIds: images.map((img) => img.id).slice(0, 5), // Log first 5 found IDs
      missingIds: imageIds
        .filter((id) => !images.find((img) => img.id === id))
        .slice(0, 5),
    });

    return images.map((image) => ({
      entityType: 'image',
      entityId: image.id,
      title: image.prompt || 'Untitled Image',
      description: undefined,
      thumbnail: image.thumbnailUrl,
      owner: {
        id: image.user?.id || image.userId,
        username: image.user?.username || 'unknown',
      },
      engagement: {
        likes: image.likes || 0,
        comments: image.comments || 0,
        engagementScore: (image.likes || 0) + (image.comments || 0) * 2,
      },
      metadata: image.metadata || {},
      tags: image.searchTags || [],
      createdAt: image.createdAt,
      updatedAt: image.updatedAt,
      privacy: image.privacy || 'public',
      isUnsafe: image.isUnsafe || false,
    }));
  }

  /**
   * Get multiple videos as unified entities
   */
  private async getVideosAsUnifiedEntities(
    videoIds: string[],
  ): Promise<UnifiedEntity[]> {
    if (videoIds.length === 0) return [];

    const videos = await this.videoRepository.find({
      where: { id: In(videoIds), deletedAt: null },
      relations: ['user'],
    });

    return videos.map((video) => ({
      entityType: 'video',
      entityId: video.id,
      title: video.prompt || 'Untitled Video',
      description: video.description,
      thumbnail: video.thumbnailUrl,
      owner: {
        id: video.user?.id || video.userId,
        username: video.user?.username || 'unknown',
      },
      engagement: {
        likes: video.likes || 0,
        comments: video.comments || 0,
        engagementScore: (video.likes || 0) + (video.comments || 0) * 2,
      },
      metadata: video.metadata || {},
      tags: video.searchTags || [],
      createdAt: video.createdAt,
      updatedAt: video.updatedAt,
      privacy: video.privacy || 'public',
      isUnsafe: video.isUnsafe || false,
    }));
  }
}
