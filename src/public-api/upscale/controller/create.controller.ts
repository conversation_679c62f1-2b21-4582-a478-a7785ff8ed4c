import { Controller, Post, Request, UseGuards } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtIntegrationAuthGuard } from 'src/auth/service/jwt-integration-auth.guard';
import { UserProvider } from 'src/user/service/provider';
import { UpscaleDto } from '../dto/upscale.dto';
import { UpscaleRequest } from '../dto/upscale.request';
import { UpscaleRequestManager } from '../service/request-manager';
import { UpscaleResponseMapper } from '../service/response-mapper';

@ApiTags('upscale')
@Controller('upscale')
@ApiBearerAuth()
export class CreateController {
  constructor(
    private requestManager: UpscaleRequestManager,
    private responseMapper: UpscaleResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @ApiOperation({
    operationId: 'upscale_create',
    summary: 'Generate a new upscale',
    description:
      'Creates a new upscale request for an existing image.\n\n' +
      'Request Body:\n' +
      '- prompt: Optional text prompt to guide the upscale\n' +
      '- imageId: UUID of the source image (required if imageUrl not provided)\n' +
      '- imageUrl: Direct URL to the image (required if imageId not provided)\n' +
      '- strength: Upscale strength factor from 1 to 5 (default: 1)\n' +
      '- organizationId: Organization UUID to deduct credits from (user must be a member)\n' +
      '- webhookUrl: Optional URL to receive a POST notification when upscale is complete\n\n' +
      'Credit Deduction:\n' +
      '- If organizationId is provided, credits will be deducted from the organization account\n' +
      '- If organizationId is not provided, credits will be deducted from the user account\n' +
      '- User must be a member of the organization to use organization credits',
  })
  @ApiBody({
    type: UpscaleRequest,
    description: 'The upscale request parameters',
  })
  @ApiCreatedResponse({
    type: UpscaleDto,
    description: 'The upscale request was successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: `
      Bad Request. Possible reasons:
      - Invalid image ID
      - Invalid upscale parameters
      - Source image not found
      - Source image not in ready state
      - organizationId: Must be a valid UUID format
      - Organization does not exist
    `,
  })
  @ApiResponse({
    status: 401,
    description:
      'Unauthorized. The user is not authenticated, or user is not a member of the specified organization.',
  })
  @ApiResponse({
    status: 402,
    description:
      'Payment Required. Insufficient credit balance (either user or organization credits depending on request).',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User does not have permission to upscale this image.',
  })
  @ApiResponse({
    status: 422,
    description:
      'Unprocessable Entity. The request payload contains invalid data.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. An unexpected error occurred.',
  })
  @Post()
  @UseGuards(JwtIntegrationAuthGuard)
  async create(
    @Body() requestBody: UpscaleRequest,
    @Request() request,
  ): Promise<UpscaleDto> {
    const user = await this.userProvider.get(request.user.id);

    const upscale = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(upscale);
  }
}
