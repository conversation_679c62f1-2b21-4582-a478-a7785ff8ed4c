import { ApiProperty } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
  <PERSON>,
  <PERSON>,
  ValidateIf,
} from 'class-validator';

export class UpscaleRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  @ValidateIf((o) => !o.imageUrl)
  imageId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @ValidateIf((o) => !o.imageCompletionId)
  imageUrl: string;

  @ApiProperty({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Strength must be at least 1' })
  @Max(5, { message: 'Strength must be at most 5' })
  strength = 1;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({
    description:
      'Organization ID to deduct credits from. If provided, credits will be deducted from the organization account instead of the user account. User must be a member of the organization.',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'organizationId must be a valid UUID' })
  organizationId?: string;
}
