import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { VideoManager } from '../service/manager';

@Injectable()
export class VideoInteractionListener {
  constructor(
    private readonly videoManager: VideoManager,
    private readonly logger: Logger,
  ) {}

  @OnEvent('entity.liked')
  async handleVideoLiked(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (payload.entityType !== 'video') {
      return; // Only handle video likes
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.incrementLikeCount(payload.entityId);

      this.logger.log('Video like count incremented', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        userId: payload.userId,
      });
    } catch (error) {
      this.logger.error('Failed to increment video like count', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        error: error.message,
      });
    }
  }

  @OnEvent('entity.unliked')
  async handleVideoUnliked(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (payload.entityType !== 'video') {
      return;
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.decrementLikeCount(payload.entityId);

      this.logger.log('Video like count decremented', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        userId: payload.userId,
      });
    } catch (error) {
      this.logger.error('Failed to decrement video like count', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        error: error.message,
      });
    }
  }

  @OnEvent('entity.comment.created')
  async handleVideoCommented(payload: {
    commentId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (payload.entityType !== 'video') {
      return;
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.incrementCommentCount(payload.entityId);

      this.logger.log('Video comment count incremented', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        userId: payload.userId,
      });
    } catch (error) {
      this.logger.error('Failed to increment video comment count', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        error: error.message,
      });
    }
  }

  @OnEvent('entity.comment.deleted')
  async handleVideoCommentDeleted(payload: {
    commentId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (payload.entityType !== 'video') {
      return;
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.decrementCommentCount(payload.entityId);

      this.logger.log('Video comment count decremented', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        userId: payload.userId,
      });
    } catch (error) {
      this.logger.error('Failed to decrement video comment count', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        error: error.message,
      });
    }
  }
}
