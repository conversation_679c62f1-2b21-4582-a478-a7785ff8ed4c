import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { VideoDto } from '../dto/video.dto';
import { VideoRequest } from '../dto/video.request';
import { VideoRequestManager } from '../service/request-manager';
import { VideoResponseMapper } from '../service/response-mapper';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class CreateController {
  constructor(
    private requestManager: VideoRequestManager,
    private responseMapper: VideoResponseMapper,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    operationId: 'video_create',
    summary: 'Create a new video',
    description:
      'Creates a new video animation task from an image.\n\n' +
      'Required Parameters:\n' +
      '- originalImageCompletionId: UUID of the image to animate\n\n' +
      '- imageUrl: URL of the image to animate\n\n' +
      'Optional Parameters:\n' +
      '- organizationId: UUID of the organization to deduct credits from\n' +
      '- width: Video width in pixels\n' +
      '- height: Video height in pixels\n' +
      '- resolution: Video resolution (720, 1080, etc.)\n' +
      '- prompt: Text description for animation\n' +
      '- settings: Animation settings (duration, motion, etc.)\n' +
      '- webhookUrl: URL to receive completion notifications\n' +
      '- privacy: Privacy level of the video (PUBLIC, PRIVATE, HIDDEN)\n' +
      '- hidePrompt: Hide the prompt from the video\n' +
      '- settings: Additional settings for the video\n',
  })
  @ApiBody({
    type: VideoRequest,
    description: 'Video creation parameters',
  })
  @ApiCreatedResponse({
    type: VideoDto,
    description: 'Video created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 422,
    description: 'Unprocessable Entity. Invalid request data',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async create(
    @Body() requestBody: VideoRequest,
    @Request() request,
  ): Promise<VideoDto> {
    const user = request.user;
    const video = await this.requestManager.create(requestBody, user);
    return await this.responseMapper.map(video);
  }
}
